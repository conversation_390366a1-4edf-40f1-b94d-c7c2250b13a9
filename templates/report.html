<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Domain Review Results</title>

</head>
<body>

<div class="container">
    <h1>Domain Review Results</h1>
    
    <table id="domainsTable" class="display" style="width:100%">
        <thead>
            <tr>
                <th data-column="domain">
                    <div class="header-content">
                        <span class="header-title">domain</span>
                    </div>
                </th>
                <th data-column="reversed_domain">
                    <div class="header-content">
                        <span class="header-title">reversed_domain</span>
                    </div>
                </th>
                <th data-column="ip">
                    <div class="header-content">
                        <span class="header-title">ip</span>
                    </div>
                </th>
                <th data-column="ipv6">
                    <div class="header-content">
                        <span class="header-title">ipv6</span>
                    </div>
                </th>
                <th data-column="cname">
                    <div class="header-content">
                        <span class="header-title">cname</span>
                    </div>
                </th>
                <th data-column="our">
                    <div class="header-content">
                        <span class="header-title">our</span>
                    </div>
                </th>
                <th data-column="our_redirect">
                    <div class="header-content">
                        <span class="header-title">our_redirect</span>
                    </div>
                </th>
                <th data-column="redirect">
                    <div class="header-content">
                        <span class="header-title">redirect</span>
                    </div>
                </th>
                <th data-column="https">
                    <div class="header-content">
                        <span class="header-title">https</span>
                    </div>
                </th>
                <th data-column="https_redirect">
                    <div class="header-content">
                        <span class="header-title">https_redirect</span>
                    </div>
                </th>
                <th data-column="header">
                    <div class="header-content">
                        <span class="header-title">header</span>
                    </div>
                </th>
                <th data-column="cdn">
                    <div class="header-content">
                        <span class="header-title">cdn</span>
                    </div>
                </th>
                <th data-column="cdn_provider">
                    <div class="header-content">
                        <span class="header-title">cdn_provider</span>
                    </div>
                </th>
                <th data-column="http_code">
                    <div class="header-content">
                        <span class="header-title">http_code</span>
                    </div>
                </th>
                <th data-column="header_content_type">
                    <div class="header-content">
                        <span class="header-title">header_content_type</span>
                    </div>
                </th>
                <th data-column="header_x_frame_options">
                    <div class="header-content">
                        <span class="header-title">header_x_frame_options</span>
                    </div>
                </th>
                <th data-column="header_x_xss_protection">
                    <div class="header-content">
                        <span class="header-title">header_x_xss_protection</span>
                    </div>
                </th>
            </tr>
        </thead>
        <tbody>
            {% for domain in domains %}
            <tr>
                <td>
                    {% if domain.https %}
                        <a href="https://{{ domain.domain }}" target="_blank">{{ domain.domain }}</a>
                    {% else %}
                        <a href="http://{{ domain.domain }}" target="_blank">{{ domain.domain }}</a>
                    {% endif %}
                </td>
                <td>{{ domain.reversed_domain }}</td>
                <td>
                    {% if domain.ip and domain.ip != "(NXDOMAIN)" %}
                        <a href="https://ipinfo.io/{{ domain.ip }}" target="_blank">{{ domain.ip }}</a>
                    {% else %}
                        <span class="status-empty">{{ domain.ip or '' }}</span>
                    {% endif %}
                </td>
                <td>
                    {% if domain.ipv6 and domain.ipv6 != "(NXDOMAIN)" %}
                        <span>{{ domain.ipv6 }}</span>
                    {% else %}
                        <span class="status-empty">{{ domain.ipv6 or '' }}</span>
                    {% endif %}
                </td>
                <td>{{ domain.cname or '' }}</td>
                <td>
                    {% if domain.our %}
                        <span class="status-yes">{{ domain.our }}</span>
                    {% else %}
                        {{ domain.our or '' }}
                    {% endif %}
                </td>
                <td>
                    {% if domain.our_redirect %}
                        <span class="status-yes">{{ domain.our_redirect }}</span>
                    {% else %}
                        {{ domain.our_redirect or '' }}
                    {% endif %}
                </td>
                <td>
                    {% if domain.redirect %}
                        <a href="https://{{ domain.redirect }}" target="_blank">{{ domain.redirect }}</a>
                    {% else %}
                        {{ domain.redirect or '' }}
                    {% endif %}
                </td>
                <td>
                    {% if domain.https %}
                        <span class="status-yes">{{ domain.https }}</span>
                    {% else %}
                        {{ domain.https or '' }}
                    {% endif %}
                </td>
                <td>{{ domain.https_redirect or '' }}</td>
                <td>
                    {% if domain.header %}
                        <span class="status-{{ 'yes' if domain.header != 'no' else 'no' }}">{{ domain.header }}</span>
                    {% else %}
                        {{ domain.header or '' }}
                    {% endif %}
                </td>
                <td>{{ domain.cdn or '' }}</td>
                <td>{{ domain.cdn_provider or '' }}</td>
                <td>{{ domain.http_code or '' }}</td>
                <td>{{ domain.header_content_type or '' }}</td>
                <td>{{ domain.header_x_frame_options or '' }}</td>
                <td>{{ domain.header_x_xss_protection or '' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

</body>
</html>