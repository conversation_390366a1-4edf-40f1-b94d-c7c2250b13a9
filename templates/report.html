<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Domain Review Results</title>

</head>
<body>

<div class="container">
    <h1>Domain Review Results</h1>
    
    <table id="domainsTable" class="display" style="width:100%">
        <thead>
            <tr>
                {%- for field in fieldnames %}
                <th data-column="{{ field }}">
                    <div class="header-content">
                        <span class="header-title">{{ field }}</span>
                    </div>
                </th>
                {%- endfor %}
            </tr>
        </thead>
        <tbody>
            {%- for domain_row in data %}
            <tr>
                {%- for field in fieldnames %}
                <td>
                    {%- set cell_data = domain_row.get(field) -%}
                    {%- if field == 'domain' -%}
                        {%- set https_cell = domain_row.get('https') -%}
                        {%- if https_cell and https_cell.value == 'yes' -%}
                            <a href="https://{{ cell_data.value }}" target="_blank">{{ cell_data.value }}</a>
                        {%- else -%}
                            <a href="http://{{ cell_data.value }}" target="_blank">{{ cell_data.value }}</a>
                        {%- endif -%}
                    {%- elif field == 'ip' -%}
                        {%- if cell_data.value and cell_data.value != "(NXDOMAIN)" -%}
                            <a href="https://ipinfo.io/{{ cell_data.value }}" target="_blank">{{ cell_data.value }}</a>
                        {%- else -%}
                            <span class="status-empty">{{ cell_data.value or '' }}</span>
                        {%- endif -%}
                    {%- elif field == 'ipv6' -%}
                        {%- if cell_data.value and cell_data.value != "(NXDOMAIN)" -%}
                            <span>{{ cell_data.value }}</span>
                        {%- else -%}
                            <span class="status-empty">{{ cell_data.value or '' }}</span>
                        {%- endif -%}
                    {%- elif field == 'redirect' -%}
                        {%- if cell_data.value -%}
                            <a href="https://{{ cell_data.value }}" target="_blank">{{ cell_data.value }}</a>
                        {%- else -%}
                            {{ cell_data.value or '' }}
                        {%- endif -%}
                    {%- elif field in ['our', 'our_redirect', 'https'] -%}
                        {%- if cell_data.value == 'yes' -%}
                            <span class="status-yes">{{ cell_data.value }}</span>
                        {%- else -%}
                            {{ cell_data.value or '' }}
                        {%- endif -%}
                    {%- elif field == 'header' -%}
                        {%- if cell_data.value -%}
                            <span class="status-{{ 'yes' if cell_data.value != 'no' else 'no' }}">{{ cell_data.value }}</span>
                        {%- else -%}
                            {{ cell_data.value or '' }}
                        {%- endif -%}
                    {%- else -%}
                        {{ cell_data.value or '' }}
                    {%- endif -%}
                </td>
                {%- endfor %}
            </tr>
            {%- endfor %}
        </tbody>
    </table>
</div>

</body>
</html>